{"name": "@fastify/busboy", "version": "3.1.1", "private": false, "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kibertoad"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/uzlopak"}], "description": "A streaming parser for HTML form data for node.js", "main": "lib/main", "type": "commonjs", "types": "lib/main.d.ts", "scripts": {"bench:busboy": "cd benchmarks && npm install && npm run benchmark-fastify", "bench:dicer": "node bench/dicer/dicer-bench-multipart-parser.js", "coveralls": "nyc report --reporter=lcov", "lint": "npm run lint:standard", "lint:fix": "standard --fix", "lint:standard": "standard --verbose | snazzy", "test:unit": "c8 --statements 98 --branches 97 --functions 96 --lines 98 node --test", "test:types": "tsd", "test": "npm run test:unit && npm run test:types"}, "devDependencies": {"@types/node": "^22.0.0", "busboy": "^1.6.0", "c8": "^10.1.2", "photofinish": "^1.8.0", "snazzy": "^9.0.0", "standard": "^17.1.0", "tinybench": "^3.0.0", "tsd": "^0.31.0", "typescript": "~5.7.2"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/fastify/busboy.git"}, "bugs": {"url": "https://github.com/fastify/busboy/issues"}, "homepage": "https://github.com/fastify/busboy#readme", "tsd": {"directory": "test/types", "compilerOptions": {"esModuleInterop": false, "module": "commonjs", "target": "ES2017"}}, "standard": {"globals": ["describe", "it"], "ignore": ["bench"]}, "files": ["README.md", "LICENSE", "lib/*", "deps/encoding/*", "deps/dicer/lib", "deps/streamsearch/", "deps/dicer/LICENSE"]}