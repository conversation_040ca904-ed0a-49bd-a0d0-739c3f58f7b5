com.amalpoint.admin.app-core-runtime-2.2.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\0073f776ec986342f7d8a627afdc659a\transformed\core-runtime-2.2.0\res
com.amalpoint.admin.app-jetified-window-java-1.2.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\0157f8871f2286b81f0b60455cb44671\transformed\jetified-window-java-1.2.0\res
com.amalpoint.admin.app-jetified-credentials-play-services-auth-1.2.0-rc01-2 C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.amalpoint.admin.app-jetified-appcompat-resources-1.1.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\179d597149d8b44255724080d49dad56\transformed\jetified-appcompat-resources-1.1.0\res
com.amalpoint.admin.app-jetified-savedstate-ktx-1.2.1-4 C:\Users\<USER>\.gradle\caches\transforms-3\1b2f8e1ba94273c251ea978a05cc081c\transformed\jetified-savedstate-ktx-1.2.1\res
com.amalpoint.admin.app-preference-1.2.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\1c848f8f37c5e484fe559c7671e5ae91\transformed\preference-1.2.1\res
com.amalpoint.admin.app-jetified-datastore-release-6 C:\Users\<USER>\.gradle\caches\transforms-3\2176a0aa5f6bbb15843e9d6617ca94fc\transformed\jetified-datastore-release\res
com.amalpoint.admin.app-core-1.13.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\res
com.amalpoint.admin.app-jetified-activity-1.9.3-8 C:\Users\<USER>\.gradle\caches\transforms-3\2e7a56ac1e1ab71d8216ccfddfda5f41\transformed\jetified-activity-1.9.3\res
com.amalpoint.admin.app-jetified-annotation-experimental-1.4.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\32b4d53de8036b893442a53454eef505\transformed\jetified-annotation-experimental-1.4.0\res
com.amalpoint.admin.app-jetified-activity-ktx-1.9.3-10 C:\Users\<USER>\.gradle\caches\transforms-3\332badf28ef635d4df014b0951f7dbcd\transformed\jetified-activity-ktx-1.9.3\res
com.amalpoint.admin.app-jetified-tracing-1.2.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\344804b99488c687b9494581b018a8c9\transformed\jetified-tracing-1.2.0\res
com.amalpoint.admin.app-jetified-play-services-auth-20.7.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\res
com.amalpoint.admin.app-slidingpanelayout-1.2.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\3acec481d9fcce33190fa922d3460712\transformed\slidingpanelayout-1.2.0\res
com.amalpoint.admin.app-jetified-savedstate-1.2.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\42e115ca81393979cdff36cce874f0ab\transformed\jetified-savedstate-1.2.1\res
com.amalpoint.admin.app-recyclerview-1.0.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\4346eeaa665b3533cb1f34b1539bb5b7\transformed\recyclerview-1.0.0\res
com.amalpoint.admin.app-jetified-datastore-preferences-release-16 C:\Users\<USER>\.gradle\caches\transforms-3\43b95863739acfc52281eeb9260d3a6b\transformed\jetified-datastore-preferences-release\res
com.amalpoint.admin.app-jetified-profileinstaller-1.3.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\res
com.amalpoint.admin.app-jetified-core-1.0.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\4ff43cf3d056aae38d02ffded0cc6e24\transformed\jetified-core-1.0.0\res
com.amalpoint.admin.app-jetified-lifecycle-livedata-core-ktx-2.7.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\53851e427f91564da59a935205158479\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.amalpoint.admin.app-jetified-core-ktx-1.13.1-20 C:\Users\<USER>\.gradle\caches\transforms-3\5847454f47d031053937666e3a5e82d8\transformed\jetified-core-ktx-1.13.1\res
com.amalpoint.admin.app-localbroadcastmanager-1.1.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\5e49a04227bc5c00063a0ab019bdecbf\transformed\localbroadcastmanager-1.1.0\res
com.amalpoint.admin.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\5faa35d8b556276be34f51d63001a29f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.amalpoint.admin.app-lifecycle-viewmodel-2.7.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\6066a53547e8fbcf4f45946ad85f0e5d\transformed\lifecycle-viewmodel-2.7.0\res
com.amalpoint.admin.app-transition-1.4.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\607e5dd937f355d4223d454e45c3189b\transformed\transition-1.4.1\res
com.amalpoint.admin.app-fragment-1.7.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\63d3b185799b2c098a0f6ed885ea8db1\transformed\fragment-1.7.1\res
com.amalpoint.admin.app-browser-1.8.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\6bf7b9bec3a755c3e68569d2ddbec360\transformed\browser-1.8.0\res
com.amalpoint.admin.app-jetified-lifecycle-process-2.7.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\res
com.amalpoint.admin.app-jetified-startup-runtime-1.1.1-28 C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\res
com.amalpoint.admin.app-jetified-firebase-common-21.0.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\res
com.amalpoint.admin.app-appcompat-1.1.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\799d1c16666fc18cb929ebf700d90383\transformed\appcompat-1.1.0\res
com.amalpoint.admin.app-jetified-window-1.2.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\res
com.amalpoint.admin.app-jetified-lifecycle-viewmodel-ktx-2.7.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\a3748a8ad00736d44691d03a3af6bdd6\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.amalpoint.admin.app-coordinatorlayout-1.0.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\a6152668d1a27e461f944bc1fd9e12ca\transformed\coordinatorlayout-1.0.0\res
com.amalpoint.admin.app-lifecycle-livedata-core-2.7.0-34 C:\Users\<USER>\.gradle\caches\transforms-3\a66a87692f835751c9cebc5e6aca319b\transformed\lifecycle-livedata-core-2.7.0\res
com.amalpoint.admin.app-jetified-fragment-ktx-1.7.1-35 C:\Users\<USER>\.gradle\caches\transforms-3\b8c188175ec7b416f0fdcca7a679e9cb\transformed\jetified-fragment-ktx-1.7.1\res
com.amalpoint.admin.app-jetified-firebase-messaging-24.1.2-36 C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\res
com.amalpoint.admin.app-lifecycle-livedata-2.7.0-37 C:\Users\<USER>\.gradle\caches\transforms-3\c11b0fd8d42c1350d7f4e72974d231fa\transformed\lifecycle-livedata-2.7.0\res
com.amalpoint.admin.app-jetified-lifecycle-runtime-ktx-2.7.0-38 C:\Users\<USER>\.gradle\caches\transforms-3\c215d1ae83aa15da64dde0f87332ab32\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.amalpoint.admin.app-jetified-core-common-2.0.3-39 C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\res
com.amalpoint.admin.app-lifecycle-runtime-2.7.0-40 C:\Users\<USER>\.gradle\caches\transforms-3\c3e60d8edb6a86f432de0791fcaa87bd\transformed\lifecycle-runtime-2.7.0\res
com.amalpoint.admin.app-jetified-play-services-base-18.1.0-41 C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\res
com.amalpoint.admin.app-jetified-datastore-core-release-42 C:\Users\<USER>\.gradle\caches\transforms-3\da3486b01469509e0b30ca8fa5e1a3ee\transformed\jetified-datastore-core-release\res
com.amalpoint.admin.app-jetified-play-services-basement-18.4.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\res
com.amalpoint.admin.app-jetified-credentials-1.2.0-rc01-44 C:\Users\<USER>\.gradle\caches\transforms-3\f0bf49ef16b05c0c843fc706aa261eb3\transformed\jetified-credentials-1.2.0-rc01\res
com.amalpoint.admin.app-debug-45 C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\res
com.amalpoint.admin.app-main-46 C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\res
com.amalpoint.admin.app-pngs-47 C:\Users\<USER>\Desktop\amal_app\admin_app\build\app\generated\res\pngs\debug
com.amalpoint.admin.app-resValues-48 C:\Users\<USER>\Desktop\amal_app\admin_app\build\app\generated\res\resValues\debug
com.amalpoint.admin.app-packageDebugResources-49 C:\Users\<USER>\Desktop\amal_app\admin_app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.amalpoint.admin.app-packageDebugResources-50 C:\Users\<USER>\Desktop\amal_app\admin_app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.amalpoint.admin.app-merged_res-51 C:\Users\<USER>\Desktop\amal_app\admin_app\build\app\intermediates\merged_res\debug
com.amalpoint.admin.app-packaged_res-52 C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-53 C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-54 C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-55 C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-56 C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-57 C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-58 C:\Users\<USER>\Desktop\amal_app\admin_app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-59 C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-60 C:\Users\<USER>\Desktop\amal_app\admin_app\build\path_provider_android\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-61 C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-62 C:\Users\<USER>\Desktop\amal_app\admin_app\build\shared_preferences_android\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-63 C:\Users\<USER>\Desktop\amal_app\admin_app\build\sqflite_android\intermediates\packaged_res\debug
com.amalpoint.admin.app-packaged_res-64 C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\packaged_res\debug
