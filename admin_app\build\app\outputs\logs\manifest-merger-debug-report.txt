-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:13:5-58:19
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a94dfa6c988049f0aefc942000fc29b0\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a94dfa6c988049f0aefc942000fc29b0\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a879634026118cb88751f42ca30e307\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a879634026118cb88751f42ca30e307\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4c8b0925a6ba9848970e6cab813b1b\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4c8b0925a6ba9848970e6cab813b1b\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b05fe5cae1deaf0300fa1a4d76f73cc6\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b05fe5cae1deaf0300fa1a4d76f73cc6\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9cd710e19ce78d6fa3f33b2da11c839\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9cd710e19ce78d6fa3f33b2da11c839\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f162453abc8e56732a340f020c7f2b2\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f162453abc8e56732a340f020c7f2b2\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03f8a7d970f3da6cb61b9e49b093bbae\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03f8a7d970f3da6cb61b9e49b093bbae\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bda95856c59c0a78b2e92bd01be27a\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bda95856c59c0a78b2e92bd01be27a\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f0ba45887344f18c06d9e56eee53564\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f0ba45887344f18c06d9e56eee53564\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:1:1-79:12
MERGED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:1:1-79:12
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\amal_app\admin_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c848f8f37c5e484fe559c7671e5ae91\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\799d1c16666fc18cb929ebf700d90383\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a94dfa6c988049f0aefc942000fc29b0\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\f0bf49ef16b05c0c843fc706aa261eb3\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\39723a49baa5ade04a1903cf76696ff2\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a879634026118cb88751f42ca30e307\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\96e4c7a9456fe3d7215f08f438831d9f\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4c8b0925a6ba9848970e6cab813b1b\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b05fe5cae1deaf0300fa1a4d76f73cc6\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\66886fababfbbe9885ce5d27677cf234\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9cd710e19ce78d6fa3f33b2da11c839\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b8c188175ec7b416f0fdcca7a679e9cb\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\332badf28ef635d4df014b0951f7dbcd\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f162453abc8e56732a340f020c7f2b2\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4346eeaa665b3533cb1f34b1539bb5b7\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\928e83765053ad89dadc523e04243cae\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a91fcc020f05f406d846d725a58b16e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\835a2570cf17da1a8a241d94d954838d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c11b0fd8d42c1350d7f4e72974d231fa\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\53851e427f91564da59a935205158479\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3748a8ad00736d44691d03a3af6bdd6\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c215d1ae83aa15da64dde0f87332ab32\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a66a87692f835751c9cebc5e6aca319b\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5faa35d8b556276be34f51d63001a29f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6066a53547e8fbcf4f45946ad85f0e5d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e60d8edb6a86f432de0791fcaa87bd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b2f8e1ba94273c251ea978a05cc081c\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\42e115ca81393979cdff36cce874f0ab\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0157f8871f2286b81f0b60455cb44671\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3acec481d9fcce33190fa922d3460712\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\da3486b01469509e0b30ca8fa5e1a3ee\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\43b95863739acfc52281eeb9260d3a6b\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\2176a0aa5f6bbb15843e9d6617ca94fc\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c5c7e249d79de21f2ce4b357b90fa00\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03f8a7d970f3da6cb61b9e49b093bbae\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bda95856c59c0a78b2e92bd01be27a\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\63d3b185799b2c098a0f6ed885ea8db1\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\2e7a56ac1e1ab71d8216ccfddfda5f41\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\5847454f47d031053937666e3a5e82d8\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\6bf7b9bec3a755c3e68569d2ddbec360\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c7d4c722d26856d103ff879ca19ab19\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\179d597149d8b44255724080d49dad56\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e391620985e5db80586d589a189d62e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6152668d1a27e461f944bc1fd9e12ca\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f058d9fd4538e611191cad8852986607\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\607e5dd937f355d4223d454e45c3189b\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0758071f0ce2536e92352606c6c0afe0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b675a570cca888cd80fdcf6b5bbfd4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\77c9a0dfab5cb49cadfc1fab5bec1d79\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\46a08ef71ed5a2e9aa807483f6f9a167\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e49a04227bc5c00063a0ab019bdecbf\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\3c28aae3742ffa31686bc65a71c60f25\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\344804b99488c687b9494581b018a8c9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbe97735ed2dec8a325631e7e8142c9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f0ba45887344f18c06d9e56eee53564\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b248ba587e69da826930d6d52d23a468\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3498cdf14a4b96cec55f8d6d324446b4\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c2013283a5604ba01f5736491b83477\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0073f776ec986342f7d8a627afdc659a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a26885d73ff24e8edaa96c0546aac5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ff43cf3d056aae38d02ffded0cc6e24\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f239fcfe089429365be6e8b9a9bd81\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e630f2f65ba35961567e8fa364da9ea\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\32b4d53de8036b893442a53454eef505\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e43d6361854f7736bfc4b90a1d1c94d4\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\8f296c697c2c0d9c58d48c05dbd6a75e\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a5c8ea197c6ef6096c48349673c51041\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:3:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:3:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:4:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:4:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:8:5-68
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:8:22-65
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:9:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:9:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:11:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:11:22-79
queries
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:64:5-78:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:65:9-68:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:66:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:66:21-70
data
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:19-48
intent#action:name:android.intent.action.VIEW+data:scheme:https
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:70:9-73:18
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:13-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:21-62
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:74:9-77:18
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\amal_app\admin_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\amal_app\admin_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c848f8f37c5e484fe559c7671e5ae91\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c848f8f37c5e484fe559c7671e5ae91\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\799d1c16666fc18cb929ebf700d90383\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\799d1c16666fc18cb929ebf700d90383\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a94dfa6c988049f0aefc942000fc29b0\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a94dfa6c988049f0aefc942000fc29b0\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\f0bf49ef16b05c0c843fc706aa261eb3\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\f0bf49ef16b05c0c843fc706aa261eb3\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\39723a49baa5ade04a1903cf76696ff2\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\39723a49baa5ade04a1903cf76696ff2\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a879634026118cb88751f42ca30e307\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a879634026118cb88751f42ca30e307\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\96e4c7a9456fe3d7215f08f438831d9f\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\96e4c7a9456fe3d7215f08f438831d9f\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4c8b0925a6ba9848970e6cab813b1b\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a4c8b0925a6ba9848970e6cab813b1b\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b05fe5cae1deaf0300fa1a4d76f73cc6\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b05fe5cae1deaf0300fa1a4d76f73cc6\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\66886fababfbbe9885ce5d27677cf234\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\66886fababfbbe9885ce5d27677cf234\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9cd710e19ce78d6fa3f33b2da11c839\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9cd710e19ce78d6fa3f33b2da11c839\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b8c188175ec7b416f0fdcca7a679e9cb\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b8c188175ec7b416f0fdcca7a679e9cb\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\332badf28ef635d4df014b0951f7dbcd\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\332badf28ef635d4df014b0951f7dbcd\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f162453abc8e56732a340f020c7f2b2\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f162453abc8e56732a340f020c7f2b2\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4346eeaa665b3533cb1f34b1539bb5b7\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4346eeaa665b3533cb1f34b1539bb5b7\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\928e83765053ad89dadc523e04243cae\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\928e83765053ad89dadc523e04243cae\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a91fcc020f05f406d846d725a58b16e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a91fcc020f05f406d846d725a58b16e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\835a2570cf17da1a8a241d94d954838d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\835a2570cf17da1a8a241d94d954838d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c11b0fd8d42c1350d7f4e72974d231fa\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c11b0fd8d42c1350d7f4e72974d231fa\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\53851e427f91564da59a935205158479\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\53851e427f91564da59a935205158479\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3748a8ad00736d44691d03a3af6bdd6\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3748a8ad00736d44691d03a3af6bdd6\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c215d1ae83aa15da64dde0f87332ab32\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c215d1ae83aa15da64dde0f87332ab32\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a66a87692f835751c9cebc5e6aca319b\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a66a87692f835751c9cebc5e6aca319b\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5faa35d8b556276be34f51d63001a29f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5faa35d8b556276be34f51d63001a29f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6066a53547e8fbcf4f45946ad85f0e5d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6066a53547e8fbcf4f45946ad85f0e5d\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e60d8edb6a86f432de0791fcaa87bd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e60d8edb6a86f432de0791fcaa87bd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b2f8e1ba94273c251ea978a05cc081c\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b2f8e1ba94273c251ea978a05cc081c\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\42e115ca81393979cdff36cce874f0ab\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\42e115ca81393979cdff36cce874f0ab\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0157f8871f2286b81f0b60455cb44671\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0157f8871f2286b81f0b60455cb44671\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3acec481d9fcce33190fa922d3460712\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3acec481d9fcce33190fa922d3460712\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\da3486b01469509e0b30ca8fa5e1a3ee\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\da3486b01469509e0b30ca8fa5e1a3ee\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\43b95863739acfc52281eeb9260d3a6b\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\43b95863739acfc52281eeb9260d3a6b\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\2176a0aa5f6bbb15843e9d6617ca94fc\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\2176a0aa5f6bbb15843e9d6617ca94fc\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c5c7e249d79de21f2ce4b357b90fa00\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c5c7e249d79de21f2ce4b357b90fa00\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03f8a7d970f3da6cb61b9e49b093bbae\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03f8a7d970f3da6cb61b9e49b093bbae\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bda95856c59c0a78b2e92bd01be27a\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bda95856c59c0a78b2e92bd01be27a\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\63d3b185799b2c098a0f6ed885ea8db1\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\63d3b185799b2c098a0f6ed885ea8db1\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\2e7a56ac1e1ab71d8216ccfddfda5f41\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\2e7a56ac1e1ab71d8216ccfddfda5f41\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\5847454f47d031053937666e3a5e82d8\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\5847454f47d031053937666e3a5e82d8\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\6bf7b9bec3a755c3e68569d2ddbec360\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\6bf7b9bec3a755c3e68569d2ddbec360\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c7d4c722d26856d103ff879ca19ab19\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c7d4c722d26856d103ff879ca19ab19\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\179d597149d8b44255724080d49dad56\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\179d597149d8b44255724080d49dad56\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e391620985e5db80586d589a189d62e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e391620985e5db80586d589a189d62e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6152668d1a27e461f944bc1fd9e12ca\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6152668d1a27e461f944bc1fd9e12ca\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f058d9fd4538e611191cad8852986607\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f058d9fd4538e611191cad8852986607\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\607e5dd937f355d4223d454e45c3189b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\607e5dd937f355d4223d454e45c3189b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0758071f0ce2536e92352606c6c0afe0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0758071f0ce2536e92352606c6c0afe0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b675a570cca888cd80fdcf6b5bbfd4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b675a570cca888cd80fdcf6b5bbfd4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\77c9a0dfab5cb49cadfc1fab5bec1d79\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\77c9a0dfab5cb49cadfc1fab5bec1d79\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\46a08ef71ed5a2e9aa807483f6f9a167\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\46a08ef71ed5a2e9aa807483f6f9a167\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e49a04227bc5c00063a0ab019bdecbf\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e49a04227bc5c00063a0ab019bdecbf\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\3c28aae3742ffa31686bc65a71c60f25\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\3c28aae3742ffa31686bc65a71c60f25\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\344804b99488c687b9494581b018a8c9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\344804b99488c687b9494581b018a8c9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbe97735ed2dec8a325631e7e8142c9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4dbe97735ed2dec8a325631e7e8142c9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f0ba45887344f18c06d9e56eee53564\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f0ba45887344f18c06d9e56eee53564\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b248ba587e69da826930d6d52d23a468\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b248ba587e69da826930d6d52d23a468\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3498cdf14a4b96cec55f8d6d324446b4\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3498cdf14a4b96cec55f8d6d324446b4\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c2013283a5604ba01f5736491b83477\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c2013283a5604ba01f5736491b83477\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0073f776ec986342f7d8a627afdc659a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0073f776ec986342f7d8a627afdc659a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a26885d73ff24e8edaa96c0546aac5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a26885d73ff24e8edaa96c0546aac5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ff43cf3d056aae38d02ffded0cc6e24\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ff43cf3d056aae38d02ffded0cc6e24\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f239fcfe089429365be6e8b9a9bd81\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7f239fcfe089429365be6e8b9a9bd81\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e630f2f65ba35961567e8fa364da9ea\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e630f2f65ba35961567e8fa364da9ea\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\32b4d53de8036b893442a53454eef505\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\32b4d53de8036b893442a53454eef505\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e43d6361854f7736bfc4b90a1d1c94d4\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e43d6361854f7736bfc4b90a1d1c94d4\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\8f296c697c2c0d9c58d48c05dbd6a75e\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\8f296c697c2c0d9c58d48c05dbd6a75e\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a5c8ea197c6ef6096c48349673c51041\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a5c8ea197c6ef6096c48349673c51041\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\debug\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.functions.FlutterFirebaseAppRegistrar
ADDED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-128
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8527d98d44f350b83b67900a9c2c491b\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\8f296c697c2c0d9c58d48c05dbd6a75e\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\8f296c697c2c0d9c58d48c05dbd6a75e\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar
ADDED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-126
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.functions.FirebaseFunctionsKtxRegistrar
ADDED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:16:17-122
meta-data#com.google.firebase.components:com.google.firebase.functions.FunctionsRegistrar
ADDED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:19:17-111
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:50:13-52:29
	android:priority
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:50:28-51
action#com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:51:17-78
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:51:25-75
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7794f10e2cc1161d7666b6dd70a8eed0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.amalpoint.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.amalpoint.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
