{"logs": [{"outputFile": "com.amalpoint.admin.app-mergeDebugResources-49:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f0bf49ef16b05c0c843fc706aa261eb3\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,111", "endOffsets": "159,271"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2817,2926", "endColumns": "108,111", "endOffsets": "2921,3033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1c848f8f37c5e484fe559c7671e5ae91\\transformed\\preference-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,266,346,480,649,730", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "170,261,341,475,644,725,802"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6027,6197,6606,6686,7003,7172,7253", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "6092,6283,6681,6815,7167,7248,7325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\799d1c16666fc18cb929ebf700d90383\\transformed\\appcompat-1.1.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,898,989,1081,1176,1270,1372,1465,1560,1655,1746,1837,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,76,90,91,94,93,101,92,94,94,90,90,80,108,99,98,108,111,110,162,95,81", "endOffsets": "215,317,425,511,618,737,816,893,984,1076,1171,1265,1367,1460,1555,1650,1741,1832,1913,2022,2122,2221,2330,2442,2553,2716,2812,2894"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,898,989,1081,1176,1270,1372,1465,1560,1655,1746,1837,1918,2027,2127,2226,2335,2447,2558,2721,6820", "endColumns": "114,101,107,85,106,118,78,76,90,91,94,93,101,92,94,94,90,90,80,108,99,98,108,111,110,162,95,81", "endOffsets": "215,317,425,511,618,737,816,893,984,1076,1171,1265,1367,1460,1555,1650,1741,1832,1913,2022,2122,2221,2330,2442,2553,2716,2812,6897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1121d8b2f6a76f40c0684f9501a302e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4765", "endColumns": "139", "endOffsets": "4900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\28ee8a5ebc96d117de5598ee6fce01ba\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3038,3135,3237,3335,3434,3548,3653,6902", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3130,3232,3330,3429,3543,3648,3770,6998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c65eb01e63da521b268cad6e158047fd\\transformed\\jetified-play-services-base-18.1.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3775,3879,4047,4169,4279,4430,4555,4666,4905,5076,5185,5360,5488,5647,5808,5877,5943", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "3874,4042,4164,4274,4425,4550,4661,4760,5071,5180,5355,5483,5642,5803,5872,5938,6022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6bf7b9bec3a755c3e68569d2ddbec360\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6097,6288,6387,6502", "endColumns": "99,98,114,103", "endOffsets": "6192,6382,6497,6601"}}]}]}