1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.amalpoint.admin"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:5:5-67
15-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:5:22-64
16    <!-- Permissions for image picker -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:3:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- FCM Permissions -->
18-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:4:5-80
18-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:4:22-77
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:8:5-68
19-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:8:22-65
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:9:5-66
20-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:9:22-63
21    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
21-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:10:5-81
21-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:10:22-78
22    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
22-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:11:5-82
22-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:11:22-79
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:64:5-78:15
31        <intent>
31-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:65:9-68:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:66:13-72
32-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:66:21-70
33
34            <data android:mimeType="text/plain" />
34-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:13-50
34-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:19-48
35        </intent>
36        <!-- URL Launcher queries -->
37        <intent>
37-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:70:9-73:18
38            <action android:name="android.intent.action.VIEW" />
38-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:13-65
38-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:21-62
39
40            <data android:scheme="https" />
40-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:13-50
41        </intent>
42        <intent>
42-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:74:9-77:18
43            <action android:name="android.intent.action.VIEW" />
43-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:13-65
43-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:21-62
44
45            <data android:scheme="http" />
45-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:13-50
46        </intent>
47    </queries>
48
49    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
49-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
49-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-76
50    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
50-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
50-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
51    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a112df221139d6208d3a181d3124687\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
52
53    <permission
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
54        android:name="com.amalpoint.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.amalpoint.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
58
59    <application
60        android:name="android.app.Application"
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\28ee8a5ebc96d117de5598ee6fce01ba\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
62        android:debuggable="true"
63        android:extractNativeLibs="false"
64        android:icon="@mipmap/ic_launcher"
65        android:label="Amal Point Admin" >
66        <activity
67            android:name="com.amalpoint.admin.MainActivity"
68            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
69            android:exported="true"
70            android:hardwareAccelerated="true"
71            android:launchMode="singleTop"
72            android:taskAffinity=""
73            android:theme="@style/LaunchTheme"
74            android:windowSoftInputMode="adjustResize" >
75
76            <!--
77                 Specifies an Android theme to apply to this Activity as soon as
78                 the Android process has started. This theme is visible to the user
79                 while the Flutter UI initializes. After that, this theme continues
80                 to determine the Window background behind the Flutter UI.
81            -->
82            <meta-data
83                android:name="io.flutter.embedding.android.NormalTheme"
84                android:resource="@style/NormalTheme" />
85
86            <intent-filter>
87                <action android:name="android.intent.action.MAIN" />
88
89                <category android:name="android.intent.category.LAUNCHER" />
90            </intent-filter>
91            <!-- FCM Intent Filter -->
92            <intent-filter>
93                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
94
95                <category android:name="android.intent.category.DEFAULT" />
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
96            </intent-filter>
97        </activity>
98
99        <!-- FCM Service -->
100        <service
101            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
102            android:exported="false" >
103            <intent-filter>
103-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:50:13-52:29
104                <action android:name="com.google.firebase.MESSAGING_EVENT" />
104-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:51:17-78
104-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:51:25-75
105            </intent-filter>
106        </service>
107        <!--
108             Don't delete the meta-data below.
109             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
110        -->
111        <meta-data
112            android:name="flutterEmbedding"
113            android:value="2" />
114
115        <service
115-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
116            android:name="com.google.firebase.components.ComponentDiscoveryService"
116-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
117            android:directBootAware="true"
117-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
118            android:exported="false" >
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
119            <meta-data
119-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
120                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
120-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
122            <meta-data
122-->[:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
123                android:name="com.google.firebase.components:io.flutter.plugins.firebase.functions.FlutterFirebaseAppRegistrar"
123-->[:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-128
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[:cloud_functions] C:\Users\<USER>\Desktop\amal_app\admin_app\build\cloud_functions\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
125            <meta-data
125-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
126                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
126-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
128            <meta-data
128-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
129                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
129-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
131            <meta-data
131-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
132                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
132-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-126
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
134            <meta-data
134-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
135                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
135-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
137            <meta-data
137-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
138                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
138-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
140            <meta-data
140-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
141                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
141-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
143            <meta-data
143-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
144                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
144-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3eb78c913a56c1ade8c509ad2c8441c0\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
146            <meta-data
146-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:15:13-17:85
147                android:name="com.google.firebase.components:com.google.firebase.functions.FirebaseFunctionsKtxRegistrar"
147-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:16:17-122
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:17:17-82
149            <meta-data
149-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:18:13-20:85
150                android:name="com.google.firebase.components:com.google.firebase.functions.FunctionsRegistrar"
150-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:19:17-111
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eae7ccb78a32519639ca1876b6ca2883\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:20:17-82
152            <meta-data
152-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
153                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
153-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
155            <meta-data
155-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
156                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
156-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
158            <meta-data
158-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
159                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
159-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
161            <meta-data
161-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
162                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
162-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\101d4847fe6fabde87a3451e45da2ce7\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
164            <meta-data
164-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
165                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
165-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f2384ad0fd009fe74077a616ea87058\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
167            <meta-data
167-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
168                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
168-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
170            <meta-data
170-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
171                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
171-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36043ee4c926c2b2f807928e44c1fa12\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
173            <meta-data
173-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
174                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
174-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
176            <meta-data
176-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
177                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
177-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\080e76b3cbda447b0041c4466f895e5d\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
179            <meta-data
179-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
180                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
180-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bdb7276744e61166d2a06471c201c6c2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
182            <meta-data
182-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
183                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
183-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
185            <meta-data
185-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
186                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
186-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0da21ae9916f4ac088f133b7e8e722bb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
188        </service>
189        <service
189-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
190            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
190-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
191            android:exported="false"
191-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
192            android:permission="android.permission.BIND_JOB_SERVICE" />
192-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
193
194        <receiver
194-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
195            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
195-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
196            android:exported="true"
196-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
197            android:permission="com.google.android.c2dm.permission.SEND" >
197-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
198            <intent-filter>
198-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
199                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
199-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
199-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
200            </intent-filter>
201        </receiver>
202
203        <provider
203-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
204            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
204-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
205            android:authorities="com.amalpoint.admin.flutterfirebasemessaginginitprovider"
205-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
206            android:exported="false"
206-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
207            android:initOrder="99" />
207-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
208        <provider
208-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
209            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
209-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
210            android:authorities="com.amalpoint.admin.flutter.image_provider"
210-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
211            android:exported="false"
211-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
212            android:grantUriPermissions="true" >
212-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
213            <meta-data
213-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
214                android:name="android.support.FILE_PROVIDER_PATHS"
214-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
215                android:resource="@xml/flutter_image_picker_file_paths" />
215-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
216        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
217        <service
217-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
218            android:name="com.google.android.gms.metadata.ModuleDependencies"
218-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
219            android:enabled="false"
219-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
220            android:exported="false" >
220-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
221            <intent-filter>
221-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
222                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
222-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
222-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
223            </intent-filter>
224
225            <meta-data
225-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
226                android:name="photopicker_activity:0:required"
226-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
227                android:value="" />
227-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
228        </service>
229        <!--
230           Declares a provider which allows us to store files to share in
231           '.../caches/share_plus' and grant the receiving action access
232        -->
233        <provider
233-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
234            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
234-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
235            android:authorities="com.amalpoint.admin.flutter.share_provider"
235-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
236            android:exported="false"
236-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
237            android:grantUriPermissions="true" >
237-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
238            <meta-data
238-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
239                android:name="android.support.FILE_PROVIDER_PATHS"
239-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
240                android:resource="@xml/flutter_share_file_paths" />
240-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
241        </provider>
242        <!--
243           This manifest declared broadcast receiver allows us to use an explicit
244           Intent when creating a PendingItent to be informed of the user's choice
245        -->
246        <receiver
246-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
247            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
247-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
248            android:exported="false" >
248-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
249            <intent-filter>
249-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
250                <action android:name="EXTRA_CHOSEN_COMPONENT" />
250-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
250-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\admin_app\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
251            </intent-filter>
252        </receiver>
253
254        <activity
254-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
255            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
255-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
256            android:exported="false"
256-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
257            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
257-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\admin_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
258        <activity
258-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
259            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
259-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
260            android:excludeFromRecents="true"
260-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
261            android:exported="true"
261-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
262            android:launchMode="singleTask"
262-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
263            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
263-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
264            <intent-filter>
264-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
265                <action android:name="android.intent.action.VIEW" />
265-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:13-65
265-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:21-62
266
267                <category android:name="android.intent.category.DEFAULT" />
267-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
267-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
268                <category android:name="android.intent.category.BROWSABLE" />
268-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
268-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
269
270                <data
270-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:13-50
271                    android:host="firebase.auth"
272                    android:path="/"
273                    android:scheme="genericidp" />
274            </intent-filter>
275        </activity>
276        <activity
276-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
277            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
277-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
278            android:excludeFromRecents="true"
278-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
279            android:exported="true"
279-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
280            android:launchMode="singleTask"
280-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
281            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
281-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
282            <intent-filter>
282-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
283                <action android:name="android.intent.action.VIEW" />
283-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:13-65
283-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:71:21-62
284
285                <category android:name="android.intent.category.DEFAULT" />
285-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
285-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
286                <category android:name="android.intent.category.BROWSABLE" />
286-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
286-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\da34b4a713105b399afae501f0c37522\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
287
288                <data
288-->C:\Users\<USER>\Desktop\amal_app\admin_app\android\app\src\main\AndroidManifest.xml:67:13-50
289                    android:host="firebase.auth"
290                    android:path="/"
291                    android:scheme="recaptcha" />
292            </intent-filter>
293        </activity>
294
295        <receiver
295-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
296            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
296-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
297            android:exported="true"
297-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
298            android:permission="com.google.android.c2dm.permission.SEND" >
298-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
299            <intent-filter>
299-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
300                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
300-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
300-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\admin_app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
301            </intent-filter>
302
303            <meta-data
303-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
304                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
304-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
305                android:value="true" />
305-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
306        </receiver>
307        <!--
308             FirebaseMessagingService performs security checks at runtime,
309             but set to not exported to explicitly avoid allowing another app to call it.
310        -->
311        <service
311-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
312            android:name="com.google.firebase.messaging.FirebaseMessagingService"
312-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
313            android:directBootAware="true"
313-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
314            android:exported="false" >
314-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
315            <intent-filter android:priority="-500" >
315-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:50:13-52:29
315-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:50:28-51
316                <action android:name="com.google.firebase.MESSAGING_EVENT" />
316-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:51:17-78
316-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\bc04d804d500455d8677a05c7800217b\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:51:25-75
317            </intent-filter>
318        </service>
319
320        <provider
320-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
321            android:name="com.google.firebase.provider.FirebaseInitProvider"
321-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
322            android:authorities="com.amalpoint.admin.firebaseinitprovider"
322-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
323            android:directBootAware="true"
323-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
324            android:exported="false"
324-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
325            android:initOrder="100" />
325-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78077822df7917bad61fe63e982b4314\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
326
327        <service
327-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
328            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
328-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
329            android:enabled="true"
329-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
330            android:exported="false" >
330-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
331            <meta-data
331-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
332                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
332-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
333                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
333-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
334        </service>
335
336        <activity
336-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
337            android:name="androidx.credentials.playservices.HiddenActivity"
337-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
338            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
338-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
339            android:enabled="true"
339-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
340            android:exported="false"
340-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
341            android:fitsSystemWindows="true"
341-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
342            android:theme="@style/Theme.Hidden" >
342-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\089c7cb8d116c0b15e7f9c7161ea029f\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
343        </activity>
344        <activity
344-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
345            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
345-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
346            android:excludeFromRecents="true"
346-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
347            android:exported="false"
347-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
348            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
348-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
349        <!--
350            Service handling Google Sign-In user revocation. For apps that do not integrate with
351            Google Sign-In, this service will never be started.
352        -->
353        <service
353-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
354            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
354-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
355            android:exported="true"
355-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
356            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
356-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
357            android:visibleToInstantApps="true" />
357-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\34a723744e755ac90b232c12e6b3e821\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
358
359        <activity
359-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
360            android:name="com.google.android.gms.common.api.GoogleApiActivity"
360-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
361            android:exported="false"
361-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
362            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
362-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c65eb01e63da521b268cad6e158047fd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
363
364        <provider
364-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
365            android:name="androidx.startup.InitializationProvider"
365-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
366            android:authorities="com.amalpoint.admin.androidx-startup"
366-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
367            android:exported="false" >
367-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
368            <meta-data
368-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
369                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
369-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
370                android:value="androidx.startup" />
370-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f1de229824e3b626519c3b4ed1eee9b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
371            <meta-data
371-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
372                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
373                android:value="androidx.startup" />
373-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
374        </provider>
375
376        <uses-library
376-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
377            android:name="androidx.window.extensions"
377-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
378            android:required="false" />
378-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
379        <uses-library
379-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
380            android:name="androidx.window.sidecar"
380-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
381            android:required="false" />
381-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a017c0c1c150e766625df730ca1d1522\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
382
383        <meta-data
383-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
384            android:name="com.google.android.gms.version"
384-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
385            android:value="@integer/google_play_services_version" />
385-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1121d8b2f6a76f40c0684f9501a302e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
386
387        <receiver
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
388            android:name="androidx.profileinstaller.ProfileInstallReceiver"
388-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
389            android:directBootAware="false"
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
390            android:enabled="true"
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
391            android:exported="true"
391-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
392            android:permission="android.permission.DUMP" >
392-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
393            <intent-filter>
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
394                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
394-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
394-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
395            </intent-filter>
396            <intent-filter>
396-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
397                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
397-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
397-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
398            </intent-filter>
399            <intent-filter>
399-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
400                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
400-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
400-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
401            </intent-filter>
402            <intent-filter>
402-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
403                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
403-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
403-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\497b559c30d96929b625a427f3dee739\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
404            </intent-filter>
405        </receiver>
406
407        <service
407-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
408            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
408-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
409            android:exported="false" >
409-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
410            <meta-data
410-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
411                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
411-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
412                android:value="cct" />
412-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\f75ad67997bb622bf80f88e39cb2c58a\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
413        </service>
414        <service
414-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
415            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
415-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
416            android:exported="false"
416-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
417            android:permission="android.permission.BIND_JOB_SERVICE" >
417-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
418        </service>
419
420        <receiver
420-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
421            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
421-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
422            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
422-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0744d7a3b2e7a8931b6302dc13ec9a1c\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
423        <activity
423-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
424            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
424-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
425            android:exported="false"
425-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
426            android:stateNotNeeded="true"
426-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
427            android:theme="@style/Theme.PlayCore.Transparent" />
427-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\c3888fac91b05cf91ee878bdbb5b9953\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
428    </application>
429
430</manifest>
