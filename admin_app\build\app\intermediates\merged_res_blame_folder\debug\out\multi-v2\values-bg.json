{"logs": [{"outputFile": "com.amalpoint.admin.app-mergeDebugResources-49:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1121d8b2f6a76f40c0684f9501a302e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4875", "endColumns": "137", "endOffsets": "5008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f0bf49ef16b05c0c843fc706aa261eb3\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,119", "endOffsets": "160,280"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2854,2964", "endColumns": "109,119", "endOffsets": "2959,3079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c65eb01e63da521b268cad6e158047fd\\transformed\\jetified-play-services-base-18.1.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3825,3934,4099,4234,4345,4512,4647,4766,5013,5182,5294,5469,5607,5764,5930,6000,6059", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "3929,4094,4229,4340,4507,4642,4761,4870,5177,5289,5464,5602,5759,5925,5995,6054,6127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6bf7b9bec3a755c3e68569d2ddbec360\\transformed\\browser-1.8.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6204,6403,6511,6623", "endColumns": "110,107,111,109", "endOffsets": "6310,6506,6618,6728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1c848f8f37c5e484fe559c7671e5ae91\\transformed\\preference-1.2.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,345,485,654,736", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "172,260,340,480,649,731,809"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6132,6315,6733,6813,7137,7306,7388", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "6199,6398,6808,6948,7301,7383,7461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\799d1c16666fc18cb929ebf700d90383\\transformed\\appcompat-1.1.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,911,1002,1094,1189,1283,1384,1477,1572,1680,1771,1862,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "220,326,431,517,627,748,828,906,997,1089,1184,1278,1379,1472,1567,1675,1766,1857,1939,2053,2161,2261,2375,2482,2590,2750,2849,2932"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,911,1002,1094,1189,1283,1384,1477,1572,1680,1771,1862,1944,2058,2166,2266,2380,2487,2595,2755,6953", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "220,326,431,517,627,748,828,906,997,1089,1184,1278,1379,1472,1567,1675,1766,1857,1939,2053,2161,2261,2375,2482,2590,2750,2849,7031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\28ee8a5ebc96d117de5598ee6fce01ba\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3084,3181,3291,3393,3494,3601,3706,7036", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3176,3286,3388,3489,3596,3701,3820,7132"}}]}]}