{"logs": [{"outputFile": "com.amalpoint.admin.app-mergeDebugResources-49:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f0bf49ef16b05c0c843fc706aa261eb3\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,270", "endColumns": "214,215", "endOffsets": "265,481"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "5503,5718", "endColumns": "214,215", "endOffsets": "5713,5929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1c848f8f37c5e484fe559c7671e5ae91\\transformed\\preference-1.2.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,276,465,642,894,1196,1378", "endColumns": "170,188,176,251,301,181,172", "endOffsets": "271,460,637,889,1191,1373,1546"}, "to": {"startLines": "38,40,44,45,48,49,50", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7361,7732,8533,8710,9345,9647,9829", "endColumns": "170,188,176,251,301,181,172", "endOffsets": "7527,7916,8705,8957,9642,9824,9997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\799d1c16666fc18cb929ebf700d90383\\transformed\\appcompat-1.1.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,310,510,719,904,1106,1321,1494,1671,1862,2055,2253,2449,2652,2847,3044,3239,3432,3623,3807,4011,4216,4417,4624,4826,5031,5303,5503", "endColumns": "204,199,208,184,201,214,172,176,190,192,197,195,202,194,196,194,192,190,183,203,204,200,206,201,204,271,199,178", "endOffsets": "305,505,714,899,1101,1316,1489,1666,1857,2050,2248,2444,2647,2842,3039,3234,3427,3618,3802,4006,4211,4412,4619,4821,5026,5298,5498,5677"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,310,510,719,904,1106,1321,1494,1671,1862,2055,2253,2449,2652,2847,3044,3239,3432,3623,3807,4011,4216,4417,4624,4826,5031,5303,8962", "endColumns": "204,199,208,184,201,214,172,176,190,192,197,195,202,194,196,194,192,190,183,203,204,200,206,201,204,271,199,178", "endOffsets": "305,505,714,899,1101,1316,1489,1666,1857,2050,2248,2444,2647,2842,3039,3234,3427,3618,3802,4006,4211,4412,4619,4821,5026,5298,5498,9136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\28ee8a5ebc96d117de5598ee6fce01ba\\transformed\\core-1.13.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "31,32,33,34,35,36,37,47", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5934,6130,6335,6536,6737,6944,7149,9141", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "6125,6330,6531,6732,6939,7144,7356,9340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6bf7b9bec3a755c3e68569d2ddbec360\\transformed\\browser-1.8.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "39,41,42,43", "startColumns": "4,4,4,4", "startOffsets": "7532,7921,8120,8331", "endColumns": "199,198,210,201", "endOffsets": "7727,8115,8326,8528"}}]}]}