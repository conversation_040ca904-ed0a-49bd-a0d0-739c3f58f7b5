{"logs": [{"outputFile": "com.amalpoint.admin.app-mergeDebugResources-49:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6bf7b9bec3a755c3e68569d2ddbec360\\transformed\\browser-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5708,5886,5981,6087", "endColumns": "95,94,105,96", "endOffsets": "5799,5976,6082,6179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\28ee8a5ebc96d117de5598ee6fce01ba\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2949,3042,3142,3239,3338,3434,3536,6468", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3037,3137,3234,3333,3429,3531,3631,6564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1121d8b2f6a76f40c0684f9501a302e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4561", "endColumns": "131", "endOffsets": "4688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\799d1c16666fc18cb929ebf700d90383\\transformed\\appcompat-1.1.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,868,959,1051,1143,1237,1338,1431,1526,1619,1710,1801,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,76,90,91,91,93,100,92,94,92,90,90,78,99,99,95,101,99,98,149,95,78", "endOffsets": "198,296,402,488,591,708,786,863,954,1046,1138,1232,1333,1426,1521,1614,1705,1796,1875,1975,2075,2171,2273,2373,2472,2622,2718,2797"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,868,959,1051,1143,1237,1338,1431,1526,1619,1710,1801,1880,1980,2080,2176,2278,2378,2477,2627,6389", "endColumns": "97,97,105,85,102,116,77,76,90,91,91,93,100,92,94,92,90,90,78,99,99,95,101,99,98,149,95,78", "endOffsets": "198,296,402,488,591,708,786,863,954,1046,1138,1232,1333,1426,1521,1614,1705,1796,1875,1975,2075,2171,2273,2373,2472,2622,2718,6463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f0bf49ef16b05c0c843fc706aa261eb3\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,114", "endOffsets": "161,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2723,2834", "endColumns": "110,114", "endOffsets": "2829,2944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1c848f8f37c5e484fe559c7671e5ae91\\transformed\\preference-1.2.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,253,327,458,627,708", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "166,248,322,453,622,703,781"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5642,5804,6184,6258,6569,6738,6819", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "5703,5881,6253,6384,6733,6814,6892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c65eb01e63da521b268cad6e158047fd\\transformed\\jetified-play-services-base-18.1.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3636,3738,3877,3999,4101,4228,4351,4459,4693,4821,4924,5069,5192,5327,5454,5514,5571", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "3733,3872,3994,4096,4223,4346,4454,4556,4816,4919,5064,5187,5322,5449,5509,5566,5637"}}]}]}