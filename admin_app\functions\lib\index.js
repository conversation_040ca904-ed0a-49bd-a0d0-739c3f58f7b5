"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.scheduledTokenCleanup = exports.cleanupInvalidTokens = exports.testNotification = exports.sendNotification = exports.forcePasswordReset = exports.changeUserPassword = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
const googleapis_1 = require("googleapis");
// Initialize Firebase Admin SDK
admin.initializeApp();
// Change user password function
exports.changeUserPassword = functions.https.onCall(async (data, context) => {
    var _a;
    // Check if the request is from an authenticated admin
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    // Check if the user has admin privileges
    const adminUser = await admin.firestore()
        .collection('users')
        .doc(context.auth.uid)
        .get();
    if (!adminUser.exists || ((_a = adminUser.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can change user passwords');
    }
    const { userId, newPassword } = data;
    if (!userId || !newPassword) {
        throw new functions.https.HttpsError('invalid-argument', 'userId and newPassword are required');
    }
    if (newPassword.length < 6) {
        throw new functions.https.HttpsError('invalid-argument', 'Password must be at least 6 characters long');
    }
    try {
        // Update the user's password using Firebase Admin SDK
        await admin.auth().updateUser(userId, {
            password: newPassword
        });
        // Update user document in Firestore to track the change
        await admin.firestore()
            .collection('users')
            .doc(userId)
            .update({
            passwordChangedByAdmin: true,
            passwordChangedAt: admin.firestore.FieldValue.serverTimestamp(),
            passwordChangeRequired: false,
            tempPassword: admin.firestore.FieldValue.delete(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        // Log the password change
        await admin.firestore()
            .collection('admin_logs')
            .add({
            action: 'password_changed',
            adminId: context.auth.uid,
            targetUserId: userId,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            details: 'Password changed by admin'
        });
        return { success: true, message: 'Password updated successfully' };
    }
    catch (error) {
        console.error('Error changing user password:', error);
        throw new functions.https.HttpsError('internal', 'Failed to change password');
    }
});
// Force password reset function
exports.forcePasswordReset = functions.https.onCall(async (data, context) => {
    var _a;
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const adminUser = await admin.firestore()
        .collection('users')
        .doc(context.auth.uid)
        .get();
    if (!adminUser.exists || ((_a = adminUser.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can force password reset');
    }
    const { userId } = data;
    if (!userId) {
        throw new functions.https.HttpsError('invalid-argument', 'userId is required');
    }
    try {
        // Get user data
        const userDoc = await admin.firestore()
            .collection('users')
            .doc(userId)
            .get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User not found');
        }
        const userData = userDoc.data();
        const userEmail = userData === null || userData === void 0 ? void 0 : userData.email;
        if (!userEmail) {
            throw new functions.https.HttpsError('invalid-argument', 'User email not found');
        }
        // Generate password reset link
        const resetLink = await admin.auth().generatePasswordResetLink(userEmail);
        // Update user document
        await admin.firestore()
            .collection('users')
            .doc(userId)
            .update({
            forcePasswordReset: true,
            passwordResetInitiatedAt: admin.firestore.FieldValue.serverTimestamp(),
            passwordResetLink: resetLink,
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        // Log the action
        await admin.firestore()
            .collection('admin_logs')
            .add({
            action: 'password_reset_forced',
            adminId: context.auth.uid,
            targetUserId: userId,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            details: 'Password reset forced by admin'
        });
        return {
            success: true,
            message: 'Password reset initiated successfully',
            resetLink: resetLink
        };
    }
    catch (error) {
        console.error('Error forcing password reset:', error);
        throw new functions.https.HttpsError('internal', 'Failed to force password reset');
    }
});
// Helper function to get OAuth2 access token for FCM v1 API
async function getAccessToken() {
    try {
        const key = require('../firebase-service-account.json');
        const jwtClient = new googleapis_1.google.auth.JWT(key.client_email, undefined, key.private_key, ['https://www.googleapis.com/auth/firebase.messaging'], undefined);
        const tokens = await jwtClient.authorize();
        if (!tokens.access_token) {
            throw new Error('Failed to obtain access token');
        }
        return tokens.access_token;
    }
    catch (error) {
        console.error('Error getting access token:', error);
        throw new Error('Authentication failed');
    }
}
// Helper function to send FCM message using v1 API
async function sendFCMMessage(token, title, body, data, imageUrl) {
    const accessToken = await getAccessToken();
    const projectId = admin.app().options.projectId;
    // Prepare data payload with navigation information
    const dataPayload = Object.assign({ screen: 'notifications', action: 'open_notifications' }, (data || {}));
    const message = {
        message: Object.assign(Object.assign({ token: token, notification: Object.assign({ title: title, body: body }, (imageUrl && { image: imageUrl })), data: dataPayload }, { android: {
                notification: {
                    click_action: 'FLUTTER_NOTIFICATION_CLICK',
                    channel_id: 'high_importance_channel'
                }
            }, apns: {
                payload: {
                    aps: {
                        category: 'GENERAL',
                        sound: 'default'
                    }
                }
            } }))
    };
    const response = await fetch(`https://fcm.googleapis.com/v1/projects/${projectId}/messages:send`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(message)
    });
    if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`FCM API error: ${response.status} - ${errorData}`);
    }
    return await response.json();
}
// Helper function to create user notification records
async function createUserNotifications(targetUsers, title, body, notificationId, type = 'system') {
    const batch = admin.firestore().batch();
    for (const userId of targetUsers) {
        const userNotificationRef = admin.firestore().collection('user_notifications').doc();
        const userNotification = {
            id: userNotificationRef.id,
            userId: userId,
            fromUserId: 'admin',
            fromUserName: 'System',
            fromUserAvatar: '',
            type: type,
            title: title,
            message: body,
            relatedId: notificationId,
            data: {
                notificationId: notificationId,
                source: 'admin'
            },
            isRead: false,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            readAt: null
        };
        batch.set(userNotificationRef, userNotification);
    }
    // Commit the batch
    await batch.commit();
}
// Send notification to all users or specific targets
exports.sendNotification = functions.https.onCall(async (data, context) => {
    var _a;
    // Check authentication
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    // Check admin privileges
    const adminUser = await admin.firestore()
        .collection('users')
        .doc(context.auth.uid)
        .get();
    if (!adminUser.exists || ((_a = adminUser.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can send notifications');
    }
    const { notificationId, title, body, imageUrl, data, type, targetUserIds, targetRoles } = data;
    // Validate input parameters
    if (!title || !body) {
        throw new functions.https.HttpsError('invalid-argument', 'Title and body are required');
    }
    if (title.length > 100) {
        throw new functions.https.HttpsError('invalid-argument', 'Title must be 100 characters or less');
    }
    if (body.length > 500) {
        throw new functions.https.HttpsError('invalid-argument', 'Body must be 500 characters or less');
    }
    if (type && !['broadcast', 'targeted', 'role'].includes(type)) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid notification type');
    }
    // Rate limiting: Check if admin has sent too many notifications recently
    const recentNotifications = await admin.firestore()
        .collection('notifications')
        .where('createdBy', '==', context.auth.uid)
        .where('createdAt', '>', admin.firestore.Timestamp.fromDate(new Date(Date.now() - 60000))) // Last minute
        .count()
        .get();
    if (recentNotifications.data().count >= 5) {
        throw new functions.https.HttpsError('resource-exhausted', 'Rate limit exceeded. Please wait before sending more notifications.');
    }
    try {
        let tokens = [];
        let targetUsers = [];
        // Get tokens based on notification type
        if (type === 'broadcast') {
            // Get all active tokens
            const tokensSnapshot = await admin.firestore()
                .collection('fcm_tokens')
                .where('isActive', '==', true)
                .get();
            tokens = tokensSnapshot.docs.map(doc => doc.data().token);
            targetUsers = tokensSnapshot.docs.map(doc => doc.data().userId);
            // Remove duplicates from targetUsers
            targetUsers = [...new Set(targetUsers)];
        }
        else if (type === 'targeted' && targetUserIds && targetUserIds.length > 0) {
            // Get tokens for specific users
            const tokensSnapshot = await admin.firestore()
                .collection('fcm_tokens')
                .where('userId', 'in', targetUserIds)
                .where('isActive', '==', true)
                .get();
            tokens = tokensSnapshot.docs.map(doc => doc.data().token);
            targetUsers = [...new Set(tokensSnapshot.docs.map(doc => doc.data().userId))];
        }
        else if (type === 'role' && targetRoles && targetRoles.length > 0) {
            // Get users by role first
            const usersSnapshot = await admin.firestore()
                .collection('users')
                .where('role', 'in', targetRoles)
                .get();
            const userIds = usersSnapshot.docs.map(doc => doc.id);
            if (userIds.length > 0) {
                // Get tokens for users with specified roles
                const tokensSnapshot = await admin.firestore()
                    .collection('fcm_tokens')
                    .where('userId', 'in', userIds)
                    .where('isActive', '==', true)
                    .get();
                tokens = tokensSnapshot.docs.map(doc => doc.data().token);
                targetUsers = [...new Set(tokensSnapshot.docs.map(doc => doc.data().userId))];
            }
        }
        if (tokens.length === 0) {
            throw new functions.https.HttpsError('not-found', 'No valid tokens found for the specified targets');
        }
        // Send notifications in batches
        const batchSize = 500; // FCM v1 API limit
        const results = [];
        let successCount = 0;
        let failureCount = 0;
        const invalidTokens = [];
        for (let i = 0; i < tokens.length; i += batchSize) {
            const batch = tokens.slice(i, i + batchSize);
            for (const token of batch) {
                try {
                    // Add navigation data to the payload
                    const notificationData = Object.assign(Object.assign({}, data), { screen: 'notifications', notificationId: notificationId });
                    await sendFCMMessage(token, title, body, notificationData, imageUrl);
                    successCount++;
                }
                catch (error) {
                    failureCount++;
                    console.error(`Failed to send to token ${token}:`, error);
                    // Check if token is invalid
                    if (error.message.includes('UNREGISTERED') ||
                        error.message.includes('INVALID_ARGUMENT')) {
                        invalidTokens.push(token);
                    }
                }
            }
        }
        // Clean up invalid tokens
        if (invalidTokens.length > 0) {
            const batch = admin.firestore().batch();
            for (const token of invalidTokens) {
                const tokenQuery = await admin.firestore()
                    .collection('fcm_tokens')
                    .where('token', '==', token)
                    .limit(1)
                    .get();
                if (!tokenQuery.empty) {
                    batch.update(tokenQuery.docs[0].ref, { isActive: false });
                }
            }
            await batch.commit();
        }
        // Create user notification records for successful sends
        if (successCount > 0 && targetUsers.length > 0) {
            try {
                await createUserNotifications(targetUsers, title, body, notificationId, type);
                console.log(`Created ${targetUsers.length} user notification records`);
            }
            catch (error) {
                console.error('Error creating user notifications:', error);
                // Don't fail the entire operation if user notifications fail
            }
        }
        // Update notification document with results
        if (notificationId) {
            await admin.firestore()
                .collection('notifications')
                .doc(notificationId)
                .update({
                isSent: true,
                totalRecipients: tokens.length,
                successfulSends: successCount,
                failedSends: failureCount,
                sentAt: admin.firestore.FieldValue.serverTimestamp(),
                invalidTokensRemoved: invalidTokens.length
            });
        }
        // Log the notification send
        await admin.firestore()
            .collection('admin_logs')
            .add({
            action: 'notification_sent',
            adminId: context.auth.uid,
            notificationId: notificationId,
            type: type,
            totalRecipients: tokens.length,
            successfulSends: successCount,
            failedSends: failureCount,
            timestamp: admin.firestore.FieldValue.serverTimestamp()
        });
        return {
            success: true,
            totalRecipients: tokens.length,
            successfulSends: successCount,
            failedSends: failureCount,
            invalidTokensRemoved: invalidTokens.length
        };
    }
    catch (error) {
        console.error('Error sending notification:', error);
        throw new functions.https.HttpsError('internal', 'Failed to send notification');
    }
});
// Test notification function
exports.testNotification = functions.https.onCall(async (data, context) => {
    var _a;
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const adminUser = await admin.firestore()
        .collection('users')
        .doc(context.auth.uid)
        .get();
    if (!adminUser.exists || ((_a = adminUser.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can send test notifications');
    }
    const { token, title, body } = data;
    if (!token || !title || !body) {
        throw new functions.https.HttpsError('invalid-argument', 'Token, title, and body are required');
    }
    try {
        const result = await sendFCMMessage(token, title, body);
        return { success: true, result };
    }
    catch (error) {
        console.error('Error sending test notification:', error);
        return { success: false, error: error.toString() };
    }
});
// Clean up invalid tokens
exports.cleanupInvalidTokens = functions.https.onCall(async (data, context) => {
    var _a;
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const adminUser = await admin.firestore()
        .collection('users')
        .doc(context.auth.uid)
        .get();
    if (!adminUser.exists || ((_a = adminUser.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can cleanup tokens');
    }
    try {
        // Get all active tokens
        const tokensSnapshot = await admin.firestore()
            .collection('fcm_tokens')
            .where('isActive', '==', true)
            .get();
        let cleanedCount = 0;
        const batch = admin.firestore().batch();
        for (const doc of tokensSnapshot.docs) {
            const tokenData = doc.data();
            try {
                // Try to send a test message to validate token
                await sendFCMMessage(tokenData.token, 'Test', 'Token validation');
            }
            catch (error) {
                // If token is invalid, mark it as inactive
                if (error.message.includes('UNREGISTERED') ||
                    error.message.includes('INVALID_ARGUMENT')) {
                    batch.update(doc.ref, { isActive: false });
                    cleanedCount++;
                }
            }
        }
        await batch.commit();
        return {
            success: true,
            cleanedTokens: cleanedCount,
            totalTokensChecked: tokensSnapshot.docs.length
        };
    }
    catch (error) {
        console.error('Error cleaning up tokens:', error);
        throw new functions.https.HttpsError('internal', 'Failed to cleanup tokens');
    }
});
// Scheduled function to clean up old inactive tokens (runs daily)
exports.scheduledTokenCleanup = functions.pubsub.schedule('0 2 * * *')
    .timeZone('UTC')
    .onRun(async (context) => {
    try {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        // Delete tokens that have been inactive for more than 30 days
        const oldTokensSnapshot = await admin.firestore()
            .collection('fcm_tokens')
            .where('isActive', '==', false)
            .where('lastUsed', '<', admin.firestore.Timestamp.fromDate(thirtyDaysAgo))
            .get();
        const batch = admin.firestore().batch();
        oldTokensSnapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`Cleaned up ${oldTokensSnapshot.docs.length} old inactive tokens`);
        return null;
    }
    catch (error) {
        console.error('Error in scheduled token cleanup:', error);
        return null;
    }
});
//# sourceMappingURL=index.js.map